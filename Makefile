# Knowledge Server Makefile

.PHONY: build test clean wire migrate lint fmt vet deps run dev docker-build docker-run

# 变量定义
APP_NAME=knowledge-server
VERSION=$(shell git describe --tags --always --dirty)
BUILD_TIME=$(shell date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION=$(shell go version | awk '{print $$3}')
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 默认目标
all: deps wire lint test build

# 安装依赖
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# 安装开发工具
tools:
	@echo "Installing development tools..."
	go install github.com/google/wire/cmd/wire@latest
	go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest

# 生成依赖注入代码
wire:
	@echo "Generating wire code..."
	wire ./cmd/server

# 代码格式化
fmt:
	@echo "Formatting code..."
	go fmt ./...

# 代码检查
vet:
	@echo "Running go vet..."
	go vet ./...

# 代码质量检查
lint:
	@echo "Running linter..."
	golangci-lint run

# 运行测试
test:
	@echo "Running tests..."
	go test -v -race -coverprofile=coverage.out ./...

# 运行集成测试
test-integration:
	@echo "Running integration tests..."
	go test -v -tags=integration ./...

# 查看测试覆盖率
coverage: test
	@echo "Generating coverage report..."
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 构建应用
build: wire
	@echo "Building application..."
	CGO_ENABLED=0 GOOS=linux go build $(LDFLAGS) -o bin/$(APP_NAME) cmd/server/main.go cmd/server/wire_gen.go

# 构建多平台版本
build-all: wire
	@echo "Building for multiple platforms..."
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/$(APP_NAME)-linux-amd64 cmd/server/main.go cmd/server/wire_gen.go
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o bin/$(APP_NAME)-darwin-amd64 cmd/server/main.go cmd/server/wire_gen.go
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o bin/$(APP_NAME)-windows-amd64.exe cmd/server/main.go cmd/server/wire_gen.go

# 运行应用
run: build
	@echo "Running application..."
	./bin/$(APP_NAME)

# 开发模式运行
dev:
	@echo "Running in development mode..."
	go run cmd/server/main.go cmd/server/wire_gen.go

# 数据库迁移
migrate-up:
	@echo "Running database migrations..."
	migrate -path migrations -database "postgres://postgres:password@localhost/knowledge_server?sslmode=disable" up

migrate-down:
	@echo "Rolling back database migrations..."
	migrate -path migrations -database "postgres://postgres:password@localhost/knowledge_server?sslmode=disable" down

migrate-create:
	@echo "Creating new migration..."
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir migrations $$name

# 生成API文档
docs:
	@echo "Generating API documentation..."
	swag init -g cmd/server/main.go -o docs/swagger

# 清理构建文件
clean:
	@echo "Cleaning up..."
	rm -rf bin/
	rm -f coverage.out coverage.html
	go clean

# Docker构建
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Docker运行
docker-run:
	@echo "Running Docker container..."
	docker-compose up -d

# Docker停止
docker-stop:
	@echo "Stopping Docker containers..."
	docker-compose down

# 检查代码质量
quality: fmt vet lint test
	@echo "Code quality check completed"

# 完整构建流程
release: clean deps wire quality build
	@echo "Release build completed"

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  deps          - Install dependencies"
	@echo "  tools         - Install development tools"
	@echo "  wire          - Generate dependency injection code"
	@echo "  fmt           - Format code"
	@echo "  vet           - Run go vet"
	@echo "  lint          - Run linter"
	@echo "  test          - Run tests"
	@echo "  test-integration - Run integration tests"
	@echo "  coverage      - Generate test coverage report"
	@echo "  build         - Build application"
	@echo "  build-all     - Build for multiple platforms"
	@echo "  run           - Run application"
	@echo "  dev           - Run in development mode"
	@echo "  migrate-up    - Run database migrations"
	@echo "  migrate-down  - Rollback database migrations"
	@echo "  migrate-create - Create new migration"
	@echo "  docs          - Generate API documentation"
	@echo "  clean         - Clean build files"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run with Docker Compose"
	@echo "  docker-stop   - Stop Docker containers"
	@echo "  quality       - Run code quality checks"
	@echo "  release       - Complete release build"
	@echo "  help          - Show this help message"
