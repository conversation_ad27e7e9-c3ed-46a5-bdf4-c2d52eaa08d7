# 知识库服务配置文件示例
# 复制此文件为 config.yaml 并根据实际环境修改配置

# 服务器配置
server:
  port: 8080                    # 服务端口
  mode: debug                   # 运行模式: debug, release, test
  read_timeout: 30s             # 读取超时时间
  write_timeout: 30s            # 写入超时时间

# 数据库配置
database:
  host: localhost               # 数据库主机
  port: 5432                    # 数据库端口
  user: postgres                # 数据库用户名
  password: password            # 数据库密码
  dbname: knowledge_server      # 数据库名称
  sslmode: disable              # SSL模式: disable, require, verify-ca, verify-full
  max_open_conns: 25            # 最大打开连接数
  max_idle_conns: 5             # 最大空闲连接数
  conn_max_lifetime: 1h         # 连接最大生存时间
  conn_max_idle_time: 30m       # 连接最大空闲时间

# Redis配置
redis:
  addr: localhost:6379          # Redis地址
  password: ""                  # Redis密码
  db: 0                         # Redis数据库编号
  pool_size: 10                 # 连接池大小

# 向量化配置
embedding:
  provider: openai              # 向量化提供商: openai, huggingface, local
  model: text-embedding-ada-002 # 向量化模型
  api_key: your-openai-api-key  # API密钥
  dimension: 1536               # 向量维度
  timeout: 30s                  # 请求超时时间

# 日志配置
logging:
  level: info                   # 日志级别: debug, info, warn, error, fatal
  format: json                  # 日志格式: json, text
  output: stdout                # 日志输出: stdout, stderr, 或文件路径
  max_size: 100                 # 日志文件最大大小(MB)
  max_backups: 10               # 保留的日志文件数量
  max_age: 30                   # 日志文件保留天数
  compress: true                # 是否压缩旧日志文件
