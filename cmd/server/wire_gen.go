// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"knowledge-server/internal/api"
	"knowledge-server/internal/data"
	"knowledge-server/pkg/config"
	"knowledge-server/pkg/logger"
)

// Injectors from wire.go:

func InitializeApp(cfg *config.Config, logger2 logger.Logger) (*api.App, func(), error) {
	database, cleanup, err := data.NewDatabase(cfg, logger2)
	if err != nil {
		return nil, nil, err
	}
	app := api.NewApp(cfg, logger2, database)
	return app, func() {
		cleanup()
	}, nil
}
