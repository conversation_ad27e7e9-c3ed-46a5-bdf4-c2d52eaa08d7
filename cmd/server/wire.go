//go:build wireinject
// +build wireinject

package main

import (
	"github.com/google/wire"
	"knowledge-server/internal/api"
	"knowledge-server/internal/data"
	"knowledge-server/pkg/config"
	"knowledge-server/pkg/logger"
)

// InitializeApp 初始化应用程序
func InitializeApp(cfg *config.Config, logger logger.Logger) (*api.App, func(), error) {
	wire.Build(
		// 基础设施
		data.NewDatabase,
		
		// API层
		api.NewApp,
		
		// 提供配置和日志
		wire.Value(cfg),
		wire.Value(logger),
	)
	return nil, nil, nil
}
