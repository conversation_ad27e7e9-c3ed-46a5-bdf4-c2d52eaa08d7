package data

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"knowledge-server/pkg/config"
	appLogger "knowledge-server/pkg/logger"
)

// Database 数据库连接包装器
type Database struct {
	DB     *gorm.DB
	SqlDB  *sql.DB
	config *config.DatabaseConfig
	logger appLogger.Logger
}

// NewDatabase 创建新的数据库连接
func NewDatabase(cfg *config.Config, logger appLogger.Logger) (*Database, func(), error) {
	dbConfig := &cfg.Database
	
	// 创建GORM配置
	gormConfig := &gorm.Config{
		Logger: newGormLogger(logger),
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dbConfig.GetDSN()), gormConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(dbConfig.MaxOpenConns)
	sqlDB.SetMaxIdleConns(dbConfig.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(dbConfig.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(dbConfig.ConnMaxIdleTime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, nil, fmt.Errorf("failed to ping database: %w", err)
	}

	logger.Info("Database connected successfully",
		"host", dbConfig.Host,
		"port", dbConfig.Port,
		"database", dbConfig.DBName,
	)

	database := &Database{
		DB:     db,
		SqlDB:  sqlDB,
		config: dbConfig,
		logger: logger,
	}

	// 清理函数
	cleanup := func() {
		if sqlDB != nil {
			sqlDB.Close()
			logger.Info("Database connection closed")
		}
	}

	return database, cleanup, nil
}

// GetDB 获取GORM数据库实例
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// GetSqlDB 获取原生SQL数据库实例
func (d *Database) GetSqlDB() *sql.DB {
	return d.SqlDB
}

// Health 检查数据库健康状态
func (d *Database) Health() error {
	return d.SqlDB.Ping()
}

// Stats 获取数据库连接统计信息
func (d *Database) Stats() sql.DBStats {
	return d.SqlDB.Stats()
}

// gormLogger GORM日志适配器
type gormLogger struct {
	logger appLogger.Logger
}

// newGormLogger 创建GORM日志适配器
func newGormLogger(logger appLogger.Logger) logger.Interface {
	return &gormLogger{logger: logger}
}

// LogMode 设置日志模式
func (l *gormLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

// Info 信息日志
func (l *gormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Info(fmt.Sprintf(msg, data...))
}

// Warn 警告日志
func (l *gormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Warn(fmt.Sprintf(msg, data...))
}

// Error 错误日志
func (l *gormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Error(fmt.Sprintf(msg, data...))
}

// Trace SQL跟踪日志
func (l *gormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	
	fields := map[string]interface{}{
		"sql":      sql,
		"rows":     rows,
		"duration": elapsed,
	}
	
	if err != nil {
		fields["error"] = err
		l.logger.WithFields(fields).Error("SQL execution failed")
	} else {
		if elapsed > 200*time.Millisecond {
			l.logger.WithFields(fields).Warn("Slow SQL query detected")
		} else {
			l.logger.WithFields(fields).Debug("SQL executed")
		}
	}
}
