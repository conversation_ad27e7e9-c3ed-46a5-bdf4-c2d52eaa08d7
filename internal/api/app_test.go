package api

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"knowledge-server/internal/data"
	"knowledge-server/pkg/config"
	"knowledge-server/pkg/logger"
)

func TestNewApp(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Server: config.ServerConfig{
			Port: 8080,
			Mode: "test",
		},
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			User:     "postgres",
			Password: "password",
			DBName:   "test_db",
			SSLMode:  "disable",
		},
	}

	// 创建测试日志
	logConfig := config.LoggingConfig{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}
	testLogger := logger.New(logConfig)

	// 创建模拟数据库（不实际连接）
	mockDB := &data.Database{}

	t.Run("app creation", func(t *testing.T) {
		app := NewApp(cfg, testLogger, mockDB)
		
		assert.NotNil(t, app)
		assert.NotNil(t, app.config)
		assert.NotNil(t, app.logger)
		assert.NotNil(t, app.database)
		assert.NotNil(t, app.router)
	})

	t.Run("handler creation", func(t *testing.T) {
		app := NewApp(cfg, testLogger, mockDB)
		handler := app.Handler()
		
		assert.NotNil(t, handler)
	})
}

func TestHealthEndpoints(t *testing.T) {
	// 设置测试环境
	cfg := &config.Config{
		Server: config.ServerConfig{
			Mode: "test",
		},
	}
	
	logConfig := config.LoggingConfig{
		Level:  "error", // 减少测试日志输出
		Format: "json",
		Output: "stdout",
	}
	testLogger := logger.New(logConfig)
	mockDB := &data.Database{}
	
	app := NewApp(cfg, testLogger, mockDB)

	tests := []struct {
		name           string
		endpoint       string
		expectedStatus int
		checkResponse  func(t *testing.T, body string)
	}{
		{
			name:           "health endpoint",
			endpoint:       "/health",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body string) {
				assert.Contains(t, body, "status")
				assert.Contains(t, body, "ok")
				assert.Contains(t, body, "timestamp")
			},
		},
		{
			name:           "info endpoint",
			endpoint:       "/api/v1/info",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body string) {
				assert.Contains(t, body, "name")
				assert.Contains(t, body, "Knowledge Server")
				assert.Contains(t, body, "version")
			},
		},
		{
			name:           "metrics endpoint",
			endpoint:       "/metrics",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body string) {
				// Prometheus metrics should contain some basic metrics
				assert.NotEmpty(t, body)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", tt.endpoint, nil)
			require.NoError(t, err)

			rr := httptest.NewRecorder()
			app.Handler().ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)
			
			if tt.checkResponse != nil {
				tt.checkResponse(t, rr.Body.String())
			}
		})
	}
}

func TestCORSMiddleware(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{Mode: "test"},
	}
	logConfig := config.LoggingConfig{
		Level: "error", Format: "json", Output: "stdout",
	}
	testLogger := logger.New(logConfig)
	mockDB := &data.Database{}
	
	app := NewApp(cfg, testLogger, mockDB)

	t.Run("CORS headers", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/health", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		app.Handler().ServeHTTP(rr, req)

		// 检查CORS头
		assert.Equal(t, "*", rr.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, rr.Header().Get("Access-Control-Allow-Methods"), "GET")
		assert.Contains(t, rr.Header().Get("Access-Control-Allow-Headers"), "Content-Type")
	})

	t.Run("OPTIONS request", func(t *testing.T) {
		req, err := http.NewRequest("OPTIONS", "/api/v1/info", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		app.Handler().ServeHTTP(rr, req)

		assert.Equal(t, http.StatusNoContent, rr.Code)
	})
}

func TestRequestIDMiddleware(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{Mode: "test"},
	}
	logConfig := config.LoggingConfig{
		Level: "error", Format: "json", Output: "stdout",
	}
	testLogger := logger.New(logConfig)
	mockDB := &data.Database{}
	
	app := NewApp(cfg, testLogger, mockDB)

	t.Run("request ID generation", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/health", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		app.Handler().ServeHTTP(rr, req)

		// 检查响应头中是否包含Request ID
		requestID := rr.Header().Get("X-Request-ID")
		assert.NotEmpty(t, requestID)
	})

	t.Run("existing request ID preservation", func(t *testing.T) {
		existingID := "test-request-id-123"
		req, err := http.NewRequest("GET", "/health", nil)
		require.NoError(t, err)
		req.Header.Set("X-Request-ID", existingID)

		rr := httptest.NewRecorder()
		app.Handler().ServeHTTP(rr, req)

		// 检查是否保留了现有的Request ID
		responseID := rr.Header().Get("X-Request-ID")
		assert.Equal(t, existingID, responseID)
	})
}
