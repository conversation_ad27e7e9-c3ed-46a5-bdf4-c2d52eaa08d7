package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"knowledge-server/internal/data"
	"knowledge-server/pkg/config"
	"knowledge-server/pkg/logger"
)

// App 应用程序结构
type App struct {
	config   *config.Config
	logger   logger.Logger
	database *data.Database
	router   *gin.Engine
}

// NewApp 创建新的应用程序实例
func NewApp(cfg *config.Config, logger logger.Logger, database *data.Database) *App {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建路由器
	router := gin.New()

	app := &App{
		config:   cfg,
		logger:   logger,
		database: database,
		router:   router,
	}

	// 设置中间件
	app.setupMiddleware()

	// 设置路由
	app.setupRoutes()

	return app
}

// Handler 返回HTTP处理器
func (a *App) Handler() http.Handler {
	return a.router
}

// setupMiddleware 设置中间件
func (a *App) setupMiddleware() {
	// 恢复中间件
	a.router.Use(gin.Recovery())

	// 请求ID中间件
	a.router.Use(logger.RequestIDMiddleware())

	// 日志中间件
	a.router.Use(logger.GinLogger(a.logger))

	// CORS中间件
	a.router.Use(a.corsMiddleware())

	// 健康检查中间件
	a.router.Use(a.healthCheckMiddleware())
}

// setupRoutes 设置路由
func (a *App) setupRoutes() {
	// 健康检查
	a.router.GET("/health", a.healthHandler)
	a.router.GET("/ready", a.readyHandler)

	// 指标端点
	a.router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// API版本组
	v1 := a.router.Group("/api/v1")
	{
		// 基础信息
		v1.GET("/info", a.infoHandler)
		
		// 知识库相关路由将在后续模块中添加
		// knowledge := v1.Group("/knowledge-bases")
		// documents := v1.Group("/documents")
		// search := v1.Group("/search")
	}
}

// corsMiddleware CORS中间件
func (a *App) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Request-ID")
		c.Header("Access-Control-Expose-Headers", "Content-Length, X-Request-ID")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// healthCheckMiddleware 健康检查中间件
func (a *App) healthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 在每个请求中检查关键组件状态
		if c.Request.URL.Path != "/health" && c.Request.URL.Path != "/ready" {
			if err := a.database.Health(); err != nil {
				a.logger.Error("Database health check failed", "error", err)
			}
		}
		c.Next()
	}
}

// healthHandler 健康检查处理器
func (a *App) healthHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"timestamp": time.Now().Unix(),
	})
}

// readyHandler 就绪检查处理器
func (a *App) readyHandler(c *gin.Context) {
	// 检查数据库连接
	if err := a.database.Health(); err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "not ready",
			"error":  "database connection failed",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "ready",
		"timestamp": time.Now().Unix(),
		"database": "connected",
	})
}

// infoHandler 应用信息处理器
func (a *App) infoHandler(c *gin.Context) {
	stats := a.database.Stats()
	
	c.JSON(http.StatusOK, gin.H{
		"name":    "Knowledge Server",
		"version": Version,
		"build_time": BuildTime,
		"go_version": GoVersion,
		"database": gin.H{
			"max_open_connections": stats.MaxOpenConnections,
			"open_connections":     stats.OpenConnections,
			"in_use":              stats.InUse,
			"idle":                stats.Idle,
		},
	})
}

// 版本信息变量（将在main.go中设置）
var (
	Version   = "dev"
	BuildTime = "unknown"
	GoVersion = "unknown"
)
