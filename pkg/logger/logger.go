package logger

import (
	"fmt"
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
	"knowledge-server/pkg/config"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})
	WithFields(fields map[string]interface{}) Logger
	WithField(key string, value interface{}) Logger
}

// logrusLogger logrus实现
type logrusLogger struct {
	logger *logrus.Logger
	entry  *logrus.Entry
}

// New 创建新的日志实例
func New(config config.LoggingConfig) Logger {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if config.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 设置输出
	var output io.Writer
	if config.Output == "stdout" || config.Output == "" {
		output = os.Stdout
	} else {
		// 确保日志目录存在
		logDir := filepath.Dir(config.Output)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			logger.Warnf("Failed to create log directory: %v", err)
			output = os.Stdout
		} else {
			// 使用lumberjack进行日志轮转
			output = &lumberjack.Logger{
				Filename:   config.Output,
				MaxSize:    config.MaxSize,    // MB
				MaxBackups: config.MaxBackups,
				MaxAge:     config.MaxAge, // days
				Compress:   config.Compress,
			}
		}
	}
	logger.SetOutput(output)

	return &logrusLogger{
		logger: logger,
		entry:  logrus.NewEntry(logger),
	}
}

// Debug 调试日志
func (l *logrusLogger) Debug(msg string, fields ...interface{}) {
	l.entry.WithFields(l.parseFields(fields...)).Debug(msg)
}

// Info 信息日志
func (l *logrusLogger) Info(msg string, fields ...interface{}) {
	l.entry.WithFields(l.parseFields(fields...)).Info(msg)
}

// Warn 警告日志
func (l *logrusLogger) Warn(msg string, fields ...interface{}) {
	l.entry.WithFields(l.parseFields(fields...)).Warn(msg)
}

// Error 错误日志
func (l *logrusLogger) Error(msg string, fields ...interface{}) {
	l.entry.WithFields(l.parseFields(fields...)).Error(msg)
}

// Fatal 致命错误日志
func (l *logrusLogger) Fatal(msg string, fields ...interface{}) {
	l.entry.WithFields(l.parseFields(fields...)).Fatal(msg)
}

// WithFields 添加多个字段
func (l *logrusLogger) WithFields(fields map[string]interface{}) Logger {
	return &logrusLogger{
		logger: l.logger,
		entry:  l.entry.WithFields(logrus.Fields(fields)),
	}
}

// WithField 添加单个字段
func (l *logrusLogger) WithField(key string, value interface{}) Logger {
	return &logrusLogger{
		logger: l.logger,
		entry:  l.entry.WithField(key, value),
	}
}

// parseFields 解析字段参数
func (l *logrusLogger) parseFields(fields ...interface{}) logrus.Fields {
	result := make(logrus.Fields)
	
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			if key, ok := fields[i].(string); ok {
				result[key] = fields[i+1]
			}
		}
	}
	
	return result
}

// GinLogger 返回Gin中间件使用的日志函数
func GinLogger(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)

		// 获取状态码
		status := c.Writer.Status()

		// 构建完整路径
		if raw != "" {
			path = path + "?" + raw
		}

		// 记录日志
		fields := map[string]interface{}{
			"status":     status,
			"method":     c.Request.Method,
			"path":       path,
			"ip":         c.ClientIP(),
			"user_agent": c.Request.UserAgent(),
			"latency":    latency,
		}

		if len(c.Errors) > 0 {
			fields["errors"] = c.Errors.String()
		}

		if status >= 500 {
			logger.WithFields(fields).Error("HTTP request completed with server error")
		} else if status >= 400 {
			logger.WithFields(fields).Warn("HTTP request completed with client error")
		} else {
			logger.WithFields(fields).Info("HTTP request completed")
		}
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的UUID生成，实际项目中可以使用更完善的UUID库
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), rand.Intn(10000))
}
