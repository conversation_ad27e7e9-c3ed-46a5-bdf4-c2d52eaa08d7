package logger

import (
	"bytes"
	"encoding/json"
	"os"
	"strings"
	"testing"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"knowledge-server/pkg/config"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name   string
		config config.LoggingConfig
		verify func(t *testing.T, logger Logger)
	}{
		{
			name: "json format logger",
			config: config.LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "stdout",
			},
			verify: func(t *testing.T, logger Logger) {
				assert.NotNil(t, logger)
				// 验证logger类型
				logrusLogger, ok := logger.(*logrusLogger)
				assert.True(t, ok)
				assert.NotNil(t, logrusLogger.logger)
				assert.NotNil(t, logrusLogger.entry)
			},
		},
		{
			name: "text format logger",
			config: config.LoggingConfig{
				Level:  "debug",
				Format: "text",
				Output: "stdout",
			},
			verify: func(t *testing.T, logger Logger) {
				assert.NotNil(t, logger)
				logrusLogger, ok := logger.(*logrusLogger)
				assert.True(t, ok)
				
				// 验证格式化器类型
				_, isTextFormatter := logrusLogger.logger.Formatter.(*logrus.TextFormatter)
				assert.True(t, isTextFormatter)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := New(tt.config)
			tt.verify(t, logger)
		})
	}
}

func TestLogLevels(t *testing.T) {
	// 创建一个缓冲区来捕获日志输出
	var buf bytes.Buffer
	
	// 创建测试配置
	config := config.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	}
	
	logger := New(config)
	
	// 获取底层logrus logger并设置输出到缓冲区
	logrusLogger, ok := logger.(*logrusLogger)
	require.True(t, ok)
	logrusLogger.logger.SetOutput(&buf)

	tests := []struct {
		name     string
		logFunc  func(string, ...interface{})
		message  string
		level    string
	}{
		{
			name:     "debug log",
			logFunc:  logger.Debug,
			message:  "debug message",
			level:    "debug",
		},
		{
			name:     "info log",
			logFunc:  logger.Info,
			message:  "info message",
			level:    "info",
		},
		{
			name:     "warn log",
			logFunc:  logger.Warn,
			message:  "warn message",
			level:    "warning",
		},
		{
			name:     "error log",
			logFunc:  logger.Error,
			message:  "error message",
			level:    "error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			buf.Reset()
			
			tt.logFunc(tt.message, "key", "value")
			
			output := buf.String()
			assert.NotEmpty(t, output)
			
			// 解析JSON日志
			var logEntry map[string]interface{}
			err := json.Unmarshal([]byte(output), &logEntry)
			require.NoError(t, err)
			
			assert.Equal(t, tt.level, logEntry["level"])
			assert.Equal(t, tt.message, logEntry["msg"])
			assert.Equal(t, "value", logEntry["key"])
		})
	}
}

func TestWithFields(t *testing.T) {
	var buf bytes.Buffer
	
	config := config.LoggingConfig{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}
	
	logger := New(config)
	logrusLogger, ok := logger.(*logrusLogger)
	require.True(t, ok)
	logrusLogger.logger.SetOutput(&buf)

	t.Run("with fields", func(t *testing.T) {
		fields := map[string]interface{}{
			"user_id":    123,
			"request_id": "req-456",
			"action":     "test",
		}
		
		loggerWithFields := logger.WithFields(fields)
		loggerWithFields.Info("test message")
		
		output := buf.String()
		assert.NotEmpty(t, output)
		
		var logEntry map[string]interface{}
		err := json.Unmarshal([]byte(output), &logEntry)
		require.NoError(t, err)
		
		assert.Equal(t, "test message", logEntry["msg"])
		assert.Equal(t, float64(123), logEntry["user_id"]) // JSON numbers are float64
		assert.Equal(t, "req-456", logEntry["request_id"])
		assert.Equal(t, "test", logEntry["action"])
	})

	t.Run("with field", func(t *testing.T) {
		buf.Reset()
		
		loggerWithField := logger.WithField("session_id", "sess-789")
		loggerWithField.Info("session message")
		
		output := buf.String()
		var logEntry map[string]interface{}
		err := json.Unmarshal([]byte(output), &logEntry)
		require.NoError(t, err)
		
		assert.Equal(t, "session message", logEntry["msg"])
		assert.Equal(t, "sess-789", logEntry["session_id"])
	})
}

func TestParseFields(t *testing.T) {
	logger := &logrusLogger{}
	
	tests := []struct {
		name     string
		fields   []interface{}
		expected map[string]interface{}
	}{
		{
			name:     "empty fields",
			fields:   []interface{}{},
			expected: map[string]interface{}{},
		},
		{
			name:     "single key-value pair",
			fields:   []interface{}{"key", "value"},
			expected: map[string]interface{}{"key": "value"},
		},
		{
			name:     "multiple key-value pairs",
			fields:   []interface{}{"key1", "value1", "key2", 123, "key3", true},
			expected: map[string]interface{}{"key1": "value1", "key2": 123, "key3": true},
		},
		{
			name:     "odd number of fields",
			fields:   []interface{}{"key1", "value1", "key2"},
			expected: map[string]interface{}{"key1": "value1"},
		},
		{
			name:     "non-string key",
			fields:   []interface{}{123, "value", "key2", "value2"},
			expected: map[string]interface{}{"key2": "value2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logger.parseFields(tt.fields...)
			assert.Equal(t, len(tt.expected), len(result))
			
			for key, expectedValue := range tt.expected {
				actualValue, exists := result[key]
				assert.True(t, exists, "Key %s should exist", key)
				assert.Equal(t, expectedValue, actualValue)
			}
		})
	}
}

func TestFileOutput(t *testing.T) {
	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "test-log-*.log")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())
	tmpFile.Close()

	config := config.LoggingConfig{
		Level:      "info",
		Format:     "json",
		Output:     tmpFile.Name(),
		MaxSize:    1,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
	}

	logger := New(config)
	logger.Info("test file output", "key", "value")

	// 验证文件是否被创建并包含日志
	content, err := os.ReadFile(tmpFile.Name())
	require.NoError(t, err)
	
	assert.NotEmpty(t, content)
	assert.Contains(t, string(content), "test file output")
	assert.Contains(t, string(content), "key")
	assert.Contains(t, string(content), "value")
}

func TestLogLevelFiltering(t *testing.T) {
	var buf bytes.Buffer
	
	config := config.LoggingConfig{
		Level:  "warn", // 只记录warn及以上级别
		Format: "json",
		Output: "stdout",
	}
	
	logger := New(config)
	logrusLogger, ok := logger.(*logrusLogger)
	require.True(t, ok)
	logrusLogger.logger.SetOutput(&buf)

	// Debug和Info应该被过滤掉
	logger.Debug("debug message")
	logger.Info("info message")
	
	debugInfoOutput := buf.String()
	assert.Empty(t, debugInfoOutput, "Debug and Info messages should be filtered out")

	// Warn应该被记录
	logger.Warn("warn message")
	warnOutput := buf.String()
	assert.Contains(t, warnOutput, "warn message")

	buf.Reset()
	
	// Error应该被记录
	logger.Error("error message")
	errorOutput := buf.String()
	assert.Contains(t, errorOutput, "error message")
}
