package config

import (
	"os"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoad(t *testing.T) {
	tests := []struct {
		name    string
		setup   func()
		cleanup func()
		wantErr bool
	}{
		{
			name: "load with default values",
			setup: func() {
				// 确保没有配置文件
				os.Remove("config.yaml")
				os.Remove("configs/config.yaml")
			},
			cleanup: func() {},
			wantErr: false,
		},
		{
			name: "load with environment variables",
			setup: func() {
				os.Setenv("KS_SERVER_PORT", "9090")
				os.Setenv("KS_DATABASE_HOST", "testhost")
				os.Setenv("KS_LOGGING_LEVEL", "debug")
			},
			cleanup: func() {
				os.Unsetenv("KS_SERVER_PORT")
				os.Unsetenv("KS_DATABASE_HOST")
				os.Unsetenv("KS_LOGGING_LEVEL")
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			defer tt.cleanup()

			cfg, err := Load()
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			assert.NotNil(t, cfg)

			// 验证默认值
			if tt.name == "load with default values" {
				assert.Equal(t, 8080, cfg.Server.Port)
				assert.Equal(t, "debug", cfg.Server.Mode)
				assert.Equal(t, "localhost", cfg.Database.Host)
				assert.Equal(t, 5432, cfg.Database.Port)
				assert.Equal(t, "info", cfg.Logging.Level)
			}

			// 验证环境变量覆盖
			if tt.name == "load with environment variables" {
				assert.Equal(t, 9090, cfg.Server.Port)
				assert.Equal(t, "testhost", cfg.Database.Host)
				assert.Equal(t, "debug", cfg.Logging.Level)
			}
		})
	}
}

func TestValidate(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid config",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
					Mode: "debug",
				},
				Database: DatabaseConfig{
					Host:   "localhost",
					User:   "postgres",
					DBName: "test",
				},
				Logging: LoggingConfig{
					Level: "info",
				},
			},
			wantErr: false,
		},
		{
			name: "invalid port",
			config: &Config{
				Server: ServerConfig{
					Port: 0,
				},
			},
			wantErr: true,
			errMsg:  "invalid server port",
		},
		{
			name: "missing database host",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
				},
				Database: DatabaseConfig{
					Host: "",
				},
			},
			wantErr: true,
			errMsg:  "database host is required",
		},
		{
			name: "invalid log level",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
				},
				Database: DatabaseConfig{
					Host:   "localhost",
					User:   "postgres",
					DBName: "test",
				},
				Logging: LoggingConfig{
					Level: "invalid",
				},
			},
			wantErr: true,
			errMsg:  "invalid log level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validate(tt.config)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDatabaseConfig_GetDSN(t *testing.T) {
	config := &DatabaseConfig{
		Host:     "localhost",
		Port:     5432,
		User:     "postgres",
		Password: "password",
		DBName:   "testdb",
		SSLMode:  "disable",
	}

	expected := "host=localhost port=5432 user=postgres password=password dbname=testdb sslmode=disable"
	actual := config.GetDSN()

	assert.Equal(t, expected, actual)
}

func TestSetDefaults(t *testing.T) {
	// 清除可能存在的环境变量
	os.Clearenv()

	setDefaults()

	// 验证一些关键默认值
	assert.Equal(t, 8080, viper.GetInt("server.port"))
	assert.Equal(t, "debug", viper.GetString("server.mode"))
	assert.Equal(t, "localhost", viper.GetString("database.host"))
	assert.Equal(t, 5432, viper.GetInt("database.port"))
	assert.Equal(t, "info", viper.GetString("logging.level"))
	assert.Equal(t, "json", viper.GetString("logging.format"))
}
